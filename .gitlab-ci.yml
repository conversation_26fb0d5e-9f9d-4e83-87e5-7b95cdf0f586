# GitLab CI/CD Pipeline for MkDocs Documentation Site
# Builds and deploys to GitLab Pages with preview environments for merge requests

image: python:3.12-slim

variables:
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"

cache:
  paths:
    - .cache/pip
    - site/

stages:
  - validate
  - build
  - deploy
  - cleanup

# Validate documentation structure and links
validate:
  stage: validate
  before_script:
    - pip install --upgrade pip
    - pip install mkdocs mkdocs-material
    - pip install mkdocs-git-revision-date-localized-plugin
    - pip install mkdocs-git-committers-plugin-2
    - pip install mkdocs-mermaid2-plugin
    - pip install mkdocs-monorepo-plugin
  script:
    - mkdocs build --strict --verbose
    - echo "Documentation validation completed successfully"
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"
  artifacts:
    paths:
      - site/
    expire_in: 1 hour
    reports:
      junit: test-results.xml
  allow_failure: false

# Build documentation for production
build:
  stage: build
  before_script:
    - pip install --upgrade pip
    - pip install mkdocs mkdocs-material
    - pip install mkdocs-git-revision-date-localized-plugin
    - pip install mkdocs-git-committers-plugin-2
    - pip install mkdocs-mermaid2-plugin
    - pip install mkdocs-monorepo-plugin
  script:
    - echo "Building documentation site..."
    - mkdocs build --clean --verbose
    - echo "Build completed successfully"
    - ls -la site/
  artifacts:
    paths:
      - site/
    expire_in: 1 day
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

# Deploy to GitLab Pages (production)
pages:
  stage: deploy
  dependencies:
    - build
  script:
    - echo "Deploying to GitLab Pages..."
    - mv site public
    - echo "Deployment completed"
    - echo "Site will be available at: $CI_PAGES_URL"
  artifacts:
    paths:
      - public
    expire_in: 30 days
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
  environment:
    name: production
    url: $CI_PAGES_URL

# Deploy preview for merge requests
pages:preview:
  stage: deploy
  dependencies:
    - build
  before_script:
    - pip install --upgrade pip
    - pip install mkdocs mkdocs-material
    - pip install mkdocs-git-revision-date-localized-plugin
    - pip install mkdocs-git-committers-plugin-2
    - pip install mkdocs-mermaid2-plugin
    - pip install mkdocs-monorepo-plugin
  script:
    - echo "Building preview for merge request..."
    - mkdocs build --clean --verbose
    - mkdir -p public/previews/$CI_MERGE_REQUEST_IID
    - cp -r site/* public/previews/$CI_MERGE_REQUEST_IID/
    - echo "Preview deployed to: $CI_PAGES_URL/previews/$CI_MERGE_REQUEST_IID"
  artifacts:
    paths:
      - public
    expire_in: 7 days
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  environment:
    name: preview/$CI_MERGE_REQUEST_IID
    url: $CI_PAGES_URL/previews/$CI_MERGE_REQUEST_IID
    on_stop: cleanup:preview

# Cleanup preview environments when MR is closed/merged
cleanup:preview:
  stage: cleanup
  script:
    - echo "Cleaning up preview environment for MR $CI_MERGE_REQUEST_IID"
    - rm -rf public/previews/$CI_MERGE_REQUEST_IID
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
  environment:
    name: preview/$CI_MERGE_REQUEST_IID
    action: stop

# Link checking job (optional, runs on schedule)
link-check:
  stage: validate
  before_script:
    - apt-get update && apt-get install -y curl
    - pip install --upgrade pip
    - pip install mkdocs mkdocs-material
    - pip install mkdocs-git-revision-date-localized-plugin
    - pip install mkdocs-git-committers-plugin-2
    - pip install mkdocs-mermaid2-plugin
    - pip install mkdocs-monorepo-plugin
  script:
    - mkdocs build --clean
    - echo "Checking for broken links..."
    # Add link checking logic here if needed
    - echo "Link check completed"
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
  allow_failure: true

# Security scanning for documentation
security-scan:
  stage: validate
  image: 
    name: aquasec/trivy:latest
    entrypoint: [""]
  script:
    - trivy fs --exit-code 0 --no-progress --format table .
    - trivy fs --exit-code 1 --severity HIGH,CRITICAL --no-progress --format json -o trivy-results.json .
  artifacts:
    reports:
      container_scanning: trivy-results.json
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  allow_failure: true

# Performance testing for the built site
performance-test:
  stage: validate
  image: sitespeedio/sitespeed.io:latest
  script:
    - echo "Running performance tests on documentation site..."
    # This would run against the deployed preview or production site
    - echo "Performance testing completed"
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
  allow_failure: true
  artifacts:
    paths:
      - sitespeed-result/
    expire_in: 1 week

# Notification job for successful deployments
notify:success:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - echo "Documentation successfully deployed!"
    - echo "Production site: $CI_PAGES_URL"
    # Add Slack notification or other notification logic here
    - |
      if [ "$CI_COMMIT_BRANCH" = "main" ]; then
        echo "Notifying team of successful production deployment..."
        # curl -X POST -H 'Content-type: application/json' \
        #   --data '{"text":"📚 Documentation updated: '$CI_PAGES_URL'"}' \
        #   $SLACK_WEBHOOK_URL
      fi
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: on_success
  dependencies: []

# Notification job for failed deployments
notify:failure:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - echo "Documentation deployment failed!"
    # Add failure notification logic here
    - |
      echo "Notifying team of deployment failure..."
      # curl -X POST -H 'Content-type: application/json' \
      #   --data '{"text":"❌ Documentation deployment failed for commit '$CI_COMMIT_SHA'"}' \
      #   $SLACK_WEBHOOK_URL
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: on_failure
  dependencies: []
