site_name: Avira System Design Wiki
site_description: Architecture documentation and decision records for Avira systems
site_author: Avira Engineering Team
site_url: https://avira.gitlab.io/system-design

# Repository
repo_name: avira/system-design
repo_url: https://gitlab.com/avira/system-design
edit_uri: -/edit/main/docs/

# Configuration
theme:
  name: material
  language: en
  palette:
    # Palette toggle for light mode
    - scheme: default
      primary: blue
      accent: blue
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    # Palette toggle for dark mode
    - scheme: slate
      primary: blue
      accent: blue
      toggle:
        icon: material/brightness-4
        name: Switch to light mode
  font:
    text: Roboto
    code: Roboto Mono
  features:
    - navigation.tabs
    - navigation.tabs.sticky
    - navigation.sections
    - navigation.expand
    - navigation.path
    - navigation.indexes
    - navigation.top
    - search.highlight
    - search.share
    - search.suggest
    - toc.follow
    - content.code.copy
    - content.code.annotate
    - content.tabs.link
    - content.action.edit
    - content.action.view
  icon:
    repo: fontawesome/brands/gitlab
    edit: material/pencil
    view: material/eye

# Plugins
plugins:
  - search:
      lang: en
  - git-revision-date-localized:
      enable_creation_date: true
      type: timeago
  - git-committers:
      repository: avira/system-design
      branch: main
  - mermaid2:
      arguments:
        theme: |
          ^(JSON.parse(__md_get("__palette").index == 1)) ? 'dark' : 'light'
  - monorepo

# Extensions
markdown_extensions:
  - abbr
  - admonition
  - attr_list
  - def_list
  - footnotes
  - md_in_html
  - toc:
      permalink: true
      title: On this page
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.betterem:
      smart_enable: all
  - pymdownx.caret
  - pymdownx.details
  - pymdownx.emoji:
      emoji_generator: !!python/name:materialx.emoji.to_svg
      emoji_index: !!python/name:materialx.emoji.twemoji
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.keys
  - pymdownx.magiclink:
      repo_url_shorthand: true
      user: avira
      repo: system-design
  - pymdownx.mark
  - pymdownx.smartsymbols
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.tasklist:
      custom_checkbox: true
  - pymdownx.tilde

# Navigation
nav:
  - Home: index.md
  - Architecture:
    - Overview: systems/overview.md
    - Services:
      - Authentication Service: systems/auth-service/overview.md
      - User Service: systems/user-service/overview.md
      - Product Service: systems/product-service/overview.md
      - Order Service: systems/order-service/overview.md
  - ADRs:
    - Template: adr/template.md
    - ADR-0001 - Microservices Architecture: adr/0001-adopt-microservices-architecture.md
  - Guides:
    - Architecture Principles: guides/architecture-principles.md
    - Development Guidelines: guides/development-guidelines.md
    - Security Guidelines: guides/security-guidelines.md
    - Deployment Procedures: guides/deployment-procedures.md
  - Runbooks:
    - Incident Response: runbooks/incident-response.md
    - Monitoring Setup: runbooks/monitoring-setup.md
    - Backup Procedures: runbooks/backup-procedures.md
  - Diagrams:
    - System Architecture: diagrams/mermaid/system-architecture.md
    - Data Flow: diagrams/plantuml/data-flow.md
    - C4 Models: diagrams/c4/system-context.md

# Extra
extra:
  version:
    provider: mike
  social:
    - icon: fontawesome/brands/gitlab
      link: https://gitlab.com/avira/system-design
    - icon: fontawesome/brands/slack
      link: https://avira.slack.com/channels/architecture
  analytics:
    provider: google
    property: !ENV GOOGLE_ANALYTICS_KEY
  consent:
    title: Cookie consent
    description: >- 
      We use cookies to recognize your repeated visits and preferences, as well
      as to measure the effectiveness of our documentation and whether users
      find what they're searching for. With your consent, you're helping us to
      make our documentation better.

# Extra CSS and JavaScript
extra_css:
  - stylesheets/extra.css

extra_javascript:
  - javascripts/mathjax.js
  - https://polyfill.io/v3/polyfill.min.js?features=es6
  - https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js

# Copyright
copyright: Copyright &copy; 2024 Avira Engineering Team
