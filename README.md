# System Design Wiki

Architecture documentation and decision records.

## Setup

To change the remote to SSH:

```bash
git remote set-<NAME_EMAIL>:aviradigital/ProjectOrlando/guidelines/system-design.git
```

## Local Development

```bash
pip install mkdocs mkdocs-material
pip install mkdocs-git-revision-date-localized-plugin
pip install mkdocs-git-committers-plugin-2
pip install mkdocs-mermaid2-plugin
pip install mkdocs-monorepo-plugin

mkdocs serve
```

## Templates

- [ADR Template](docs/adr/template.md) - For Architecture Decision Records
- [MR Templates](.gitlab/merge_request_templates/) - For merge requests
