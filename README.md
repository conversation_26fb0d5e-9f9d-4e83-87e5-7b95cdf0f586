# Avira System Design Wiki

[![GitLab Pages](https://img.shields.io/badge/GitLab%20Pages-deployed-brightgreen)](https://avira.gitlab.io/system-design)
[![Documentation](https://img.shields.io/badge/docs-MkDocs-blue)](https://avira.gitlab.io/system-design)
[![License](https://img.shields.io/badge/license-MIT-green)](LICENSE)

> **Architecture-as-Code Documentation Hub**  
> Comprehensive system design documentation, Architecture Decision Records (ADRs), and technical guides for the Avira platform.

## 🚀 Quick Start

### View the Documentation
The documentation is automatically deployed to GitLab Pages:
**[📖 View Live Documentation](https://avira.gitlab.io/system-design)**

### Local Development

1. **Clone the repository**
   ```bash
   git clone https://gitlab.com/avira/system-design.git
   cd system-design
   ```

2. **Install dependencies**
   ```bash
   pip install mkdocs mkdocs-material
   pip install mkdocs-git-revision-date-localized-plugin
   pip install mkdocs-git-committers-plugin-2
   pip install mkdocs-mermaid2-plugin
   pip install mkdocs-monorepo-plugin
   ```

3. **Serve locally**
   ```bash
   mkdocs serve
   ```
   
   The documentation will be available at `http://localhost:8000`

4. **Build for production**
   ```bash
   mkdocs build
   ```

## 📁 Repository Structure

```
├── docs/                          # Documentation source files
│   ├── index.md                   # Homepage
│   ├── systems/                   # System documentation
│   │   ├── overview.md           # System landscape overview
│   │   ├── auth-service/         # Authentication service docs
│   │   ├── user-service/         # User service docs
│   │   ├── product-service/      # Product service docs
│   │   └── order-service/        # Order service docs
│   ├── adr/                      # Architecture Decision Records
│   │   ├── template.md           # ADR template
│   │   └── 0001-*.md            # Individual ADRs
│   ├── guides/                   # Development and operational guides
│   │   ├── architecture-principles.md
│   │   ├── development-guidelines.md
│   │   └── security-guidelines.md
│   ├── runbooks/                 # Operational runbooks
│   └── diagrams/                 # Diagrams as code
│       ├── mermaid/              # Mermaid diagrams
│       ├── plantuml/             # PlantUML diagrams
│       └── c4/                   # C4 model diagrams
├── mkdocs.yml                    # MkDocs configuration
├── .gitlab-ci.yml               # CI/CD pipeline
├── CODEOWNERS                   # Code review assignments
└── .gitlab/                     # GitLab templates
    └── merge_request_templates/
```

## 🏗️ Architecture Overview

This documentation covers the Avira platform's microservices architecture, including:

- **System Design**: High-level architecture and service interactions
- **ADRs**: Historical record of architectural decisions
- **Service Documentation**: Detailed docs for each microservice
- **Operational Guides**: Runbooks and troubleshooting guides
- **Development Guidelines**: Standards and best practices

## 📝 Contributing

### Documentation Changes

1. **Create a branch** for your changes
   ```bash
   git checkout -b docs/your-feature-name
   ```

2. **Make your changes** following our [style guide](docs/guides/documentation-style.md)

3. **Test locally** to ensure everything renders correctly
   ```bash
   mkdocs serve
   ```

4. **Submit a merge request** using the appropriate template:
   - [Default template](.gitlab/merge_request_templates/Default.md) for general changes
   - [ADR template](.gitlab/merge_request_templates/ADR.md) for Architecture Decision Records

### Architecture Decision Records (ADRs)

1. **Use the ADR template**: Copy `docs/adr/template.md`
2. **Follow the naming convention**: `NNNN-short-descriptive-title.md`
3. **Complete all sections** thoroughly
4. **Use the ADR merge request template**
5. **Get required approvals** (see CODEOWNERS)

### Diagrams as Code

We use text-based diagrams for better version control:

- **Mermaid**: For flowcharts, sequence diagrams, system flows
- **PlantUML**: For detailed UML diagrams  
- **C4 Model**: For system architecture visualization

Example Mermaid diagram:
```mermaid
graph TB
    A[Client] --> B[API Gateway]
    B --> C[Service]
    C --> D[Database]
```

## 🔄 CI/CD Pipeline

The documentation is automatically built and deployed using GitLab CI/CD:

- **Validation**: Checks documentation structure and links
- **Build**: Generates static site with MkDocs
- **Deploy**: Publishes to GitLab Pages
- **Preview**: Creates preview environments for merge requests

## 👥 Team and Ownership

Documentation ownership is defined in [CODEOWNERS](CODEOWNERS):

- **Architecture Team**: Overall architecture and ADRs
- **Service Teams**: Individual service documentation
- **DevOps Team**: Infrastructure and deployment docs
- **Security Team**: Security-related documentation

## 📊 Features

- **🎨 Modern UI**: Material Design theme with dark/light mode
- **🔍 Full-text Search**: Powered by MkDocs search
- **📱 Mobile Responsive**: Works on all devices
- **🔗 Cross-references**: Easy navigation between related docs
- **📈 Diagrams**: Interactive Mermaid and PlantUML diagrams
- **🏷️ Versioning**: Git-based version control with history
- **👥 Collaboration**: Review process with CODEOWNERS
- **🚀 Auto-deployment**: GitLab Pages with preview environments

## 🛠️ Technology Stack

- **[MkDocs](https://www.mkdocs.org/)**: Static site generator
- **[Material for MkDocs](https://squidfunk.github.io/mkdocs-material/)**: Modern theme
- **[Mermaid](https://mermaid-js.github.io/)**: Diagram generation
- **[PlantUML](https://plantuml.com/)**: UML diagrams
- **[GitLab Pages](https://docs.gitlab.com/ee/user/project/pages/)**: Hosting
- **[GitLab CI/CD](https://docs.gitlab.com/ee/ci/)**: Build and deployment

## 📞 Support

- **Documentation Issues**: Create an issue in this repository
- **Architecture Questions**: #architecture on Slack
- **Technical Support**: #engineering-help on Slack
- **Emergency**: Follow the [incident response runbook](docs/runbooks/incident-response.md)

## 📄 License

This documentation is licensed under the MIT License. See [LICENSE](LICENSE) for details.

---

**🏠 [View Documentation](https://avira.gitlab.io/system-design)** | **📝 [Contribute](CONTRIBUTING.md)** | **💬 [Discuss](https://gitlab.com/avira/system-design/-/issues)**
