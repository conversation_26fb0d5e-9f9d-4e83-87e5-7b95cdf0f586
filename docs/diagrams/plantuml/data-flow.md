# Data Flow Diagrams

This page contains PlantUML diagrams that illustrate data flow patterns and processes within our system.

## User Registration Data Flow

```plantuml
@startuml user-registration-flow
!theme plain
title User Registration Data Flow

actor User as U
participant "Web App" as WA
participant "API Gateway" as GW
participant "Auth Service" as AS
participant "User Service" as US
participant "Email Service" as ES
database "User Database" as DB
queue "Event Queue" as Q

U -> WA: Fill registration form
WA -> GW: POST /api/v1/register
activate GW

GW -> AS: Validate registration data
activate AS
AS -> AS: Check email format
AS -> AS: Validate password strength
AS -> US: Check if user exists
activate US
US -> DB: SELECT user by email
DB --> US: User not found
US --> AS: Email available
deactivate US

AS -> US: Create user account
activate US
US -> DB: INSERT user record
DB --> US: User created
US -> AS: Return user ID
deactivate US

AS -> AS: Generate JWT token
AS -> Q: Publish user_registered event
AS -> GW: Return success + token
deactivate AS

GW -> WA: Registration successful
deactivate GW
WA -> U: Show success message

Q -> ES: Process user_registered event
activate ES
ES -> ES: Generate welcome email
ES -> ES: Send email to user
deactivate ES

@enduml
```

## Order Processing Data Flow

```plantuml
@startuml order-processing-flow
!theme plain
title Order Processing Data Flow

actor Customer as C
participant "Mobile App" as MA
participant "API Gateway" as GW
participant "Order Service" as OS
participant "Product Service" as PS
participant "Payment Service" as PY
participant "Inventory Service" as IS
participant "Notification Service" as NS
database "Order DB" as ODB
database "Product DB" as PDB
database "Inventory DB" as IDB
queue "Event Bus" as EB

C -> MA: Place order
MA -> GW: POST /api/v1/orders
activate GW

GW -> OS: Create order request
activate OS

OS -> PS: Get product details
activate PS
PS -> PDB: SELECT product info
PDB --> PS: Product details
PS --> OS: Product info + price
deactivate PS

OS -> IS: Check inventory
activate IS
IS -> IDB: SELECT stock level
IDB --> IS: Stock available
IS -> IDB: RESERVE inventory
IDB --> IS: Inventory reserved
IS --> OS: Inventory confirmed
deactivate IS

OS -> ODB: INSERT order record
ODB --> OS: Order created

OS -> PY: Process payment
activate PY
PY -> PY: Validate payment method
PY -> PY: Charge customer
PY --> OS: Payment successful
deactivate PY

OS -> ODB: UPDATE order status = 'confirmed'
ODB --> OS: Order updated

OS -> EB: Publish order_confirmed event
OS --> GW: Order confirmation
deactivate OS

GW --> MA: Order success
MA --> C: Order confirmation

EB -> NS: Process order_confirmed event
activate NS
NS -> NS: Generate confirmation email
NS -> NS: Send SMS notification
NS -> NS: Push notification to app
deactivate NS

EB -> IS: Process order_confirmed event
activate IS
IS -> IDB: COMMIT inventory reservation
IDB --> IS: Inventory committed
deactivate IS

@enduml
```

## Data Synchronization Flow

```plantuml
@startuml data-sync-flow
!theme plain
title Data Synchronization Between Services

participant "User Service" as US
participant "Order Service" as OS
participant "Analytics Service" as AS
participant "Notification Service" as NS
database "User DB" as UDB
database "Order DB" as ODB
database "Analytics DB" as ADB
queue "Event Bus" as EB

note over US, NS: User Profile Update Scenario

US -> UDB: UPDATE user profile
UDB --> US: Profile updated

US -> EB: Publish user_profile_updated event
note right: Event contains:\n- user_id\n- changed_fields\n- timestamp

EB -> OS: Consume user_profile_updated
activate OS
OS -> ODB: UPDATE cached user data
ODB --> OS: Cache updated
deactivate OS

EB -> AS: Consume user_profile_updated
activate AS
AS -> ADB: INSERT user_activity record
ADB --> AS: Activity logged
deactivate AS

EB -> NS: Consume user_profile_updated
activate NS
NS -> NS: Check notification preferences
alt Profile includes email change
    NS -> NS: Send email verification
else Profile includes phone change
    NS -> NS: Send SMS verification
end
deactivate NS

note over US, NS: Event-Driven Data Consistency

@enduml
```

## Authentication Flow

```plantuml
@startuml auth-flow
!theme plain
title Authentication and Authorization Flow

actor User as U
participant "Client App" as CA
participant "API Gateway" as GW
participant "Auth Service" as AS
participant "User Service" as US
participant "Protected Service" as PS
database "User DB" as UDB
cache "Redis Cache" as RC

U -> CA: Login with credentials
CA -> GW: POST /api/v1/auth/login
activate GW

GW -> AS: Authenticate user
activate AS

AS -> UDB: SELECT user by email
UDB --> AS: User record

AS -> AS: Verify password hash
AS -> AS: Generate JWT token
AS -> RC: STORE refresh token
RC --> AS: Token stored

AS --> GW: Return JWT + refresh token
deactivate AS

GW --> CA: Authentication successful
CA -> CA: Store JWT token
CA --> U: Login successful

note over U, PS: Subsequent API Requests

U -> CA: Access protected resource
CA -> GW: GET /api/v1/protected\nAuthorization: Bearer <JWT>
activate GW

GW -> GW: Validate JWT signature
GW -> GW: Check token expiration
GW -> AS: Verify token (if needed)
activate AS
AS -> RC: CHECK token blacklist
RC --> AS: Token valid
AS --> GW: Token verified
deactivate AS

GW -> PS: Forward request with user context
activate PS
PS -> PS: Check user permissions
PS -> PS: Process request
PS --> GW: Response data
deactivate PS

GW --> CA: Protected data
CA --> U: Display data

note over U, PS: Token Refresh Flow

CA -> CA: Detect token expiration
CA -> GW: POST /api/v1/auth/refresh\nRefresh-Token: <refresh_token>
activate GW

GW -> AS: Refresh access token
activate AS
AS -> RC: VALIDATE refresh token
RC --> AS: Token valid
AS -> AS: Generate new JWT
AS -> RC: UPDATE refresh token
RC --> AS: Token updated
AS --> GW: New JWT token
deactivate AS

GW --> CA: New access token
CA -> CA: Update stored token

@enduml
```

## Microservices Communication Patterns

```plantuml
@startuml communication-patterns
!theme plain
title Microservices Communication Patterns

package "Synchronous Communication" {
    participant "Service A" as SA
    participant "Service B" as SB
    
    SA -> SB: HTTP Request
    activate SB
    SB --> SA: HTTP Response
    deactivate SB
    
    note right: Direct service-to-service\ncommunication for immediate\nresponse requirements
}

package "Asynchronous Communication" {
    participant "Publisher" as P
    queue "Message Queue" as MQ
    participant "Subscriber 1" as S1
    participant "Subscriber 2" as S2
    
    P -> MQ: Publish event
    MQ -> S1: Deliver event
    MQ -> S2: Deliver event
    
    note right: Event-driven communication\nfor loose coupling and\nscalability
}

package "Request-Reply Pattern" {
    participant "Requester" as R
    queue "Request Queue" as RQ
    queue "Reply Queue" as RPQ
    participant "Responder" as RS
    
    R -> RQ: Send request
    RQ -> RS: Deliver request
    activate RS
    RS -> RPQ: Send reply
    deactivate RS
    RPQ -> R: Deliver reply
    
    note right: Asynchronous request-reply\nfor non-blocking operations
}

package "Saga Pattern" {
    participant "Orchestrator" as O
    participant "Service 1" as S1
    participant "Service 2" as S2
    participant "Service 3" as S3
    
    O -> S1: Execute step 1
    activate S1
    S1 --> O: Success
    deactivate S1
    
    O -> S2: Execute step 2
    activate S2
    S2 --> O: Success
    deactivate S2
    
    O -> S3: Execute step 3
    activate S3
    S3 --> O: Failure
    deactivate S3
    
    O -> S2: Compensate step 2
    activate S2
    S2 --> O: Compensated
    deactivate S2
    
    O -> S1: Compensate step 1
    activate S1
    S1 --> O: Compensated
    deactivate S1
    
    note right: Distributed transaction\nmanagement with compensation
}

@enduml
```
