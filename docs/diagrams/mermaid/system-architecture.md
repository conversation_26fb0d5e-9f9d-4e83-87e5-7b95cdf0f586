# System Architecture Diagrams

This page contains Mermaid diagrams that visualize our system architecture at different levels of detail.

## High-Level System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        Web[Web Application]
        Mobile[Mobile Apps]
        API_Clients[API Clients]
    end
    
    subgraph "Edge & Gateway"
        CDN[Content Delivery Network]
        LB[Load Balancer]
        Gateway[API Gateway]
        Auth[Authentication Service]
    end
    
    subgraph "Core Services"
        UserSvc[User Service]
        ProductSvc[Product Service]
        OrderSvc[Order Service]
        PaymentSvc[Payment Service]
        NotificationSvc[Notification Service]
    end
    
    subgraph "Data Services"
        UserDB[(User Database)]
        ProductDB[(Product Database)]
        OrderDB[(Order Database)]
        Cache[(Redis Cache)]
        SearchEngine[(Elasticsearch)]
    end
    
    subgraph "External Services"
        PaymentGW[Payment Gateway]
        EmailSvc[Email Service]
        SMSSvc[SMS Service]
    end
    
    subgraph "Infrastructure"
        MessageQueue[Apache Kafka]
        Monitoring[Monitoring Stack]
        Logging[Centralized Logging]
    end
    
    Web --> CDN
    Mobile --> LB
    API_Clients --> LB
    CDN --> LB
    LB --> Gateway
    Gateway --> Auth
    
    Gateway --> UserSvc
    Gateway --> ProductSvc
    Gateway --> OrderSvc
    Gateway --> PaymentSvc
    
    UserSvc --> UserDB
    UserSvc --> Cache
    ProductSvc --> ProductDB
    ProductSvc --> SearchEngine
    OrderSvc --> OrderDB
    OrderSvc --> MessageQueue
    PaymentSvc --> PaymentGW
    
    MessageQueue --> NotificationSvc
    NotificationSvc --> EmailSvc
    NotificationSvc --> SMSSvc
    
    UserSvc --> Monitoring
    ProductSvc --> Monitoring
    OrderSvc --> Monitoring
    PaymentSvc --> Monitoring
    NotificationSvc --> Monitoring
    
    UserSvc --> Logging
    ProductSvc --> Logging
    OrderSvc --> Logging
    PaymentSvc --> Logging
    NotificationSvc --> Logging
```

## Service Communication Flow

```mermaid
sequenceDiagram
    participant Client
    participant Gateway as API Gateway
    participant Auth as Auth Service
    participant User as User Service
    participant Product as Product Service
    participant Order as Order Service
    participant Payment as Payment Service
    participant Queue as Message Queue
    participant Notification as Notification Service
    
    Client->>Gateway: Place Order Request
    Gateway->>Auth: Validate Token
    Auth-->>Gateway: Token Valid
    
    Gateway->>User: Get User Details
    User-->>Gateway: User Info
    
    Gateway->>Product: Check Product Availability
    Product-->>Gateway: Product Available
    
    Gateway->>Order: Create Order
    Order->>Payment: Process Payment
    Payment-->>Order: Payment Confirmed
    
    Order->>Queue: Publish Order Created Event
    Order-->>Gateway: Order Created
    Gateway-->>Client: Order Confirmation
    
    Queue->>Notification: Order Created Event
    Notification->>Notification: Send Confirmation Email
    Notification->>Notification: Send SMS Update
```

## Data Flow Architecture

```mermaid
flowchart LR
    subgraph "Data Sources"
        UserActions[User Actions]
        APIRequests[API Requests]
        SystemEvents[System Events]
    end
    
    subgraph "Data Ingestion"
        Gateway[API Gateway]
        EventBus[Event Bus]
        LogCollector[Log Collector]
    end
    
    subgraph "Processing"
        Services[Microservices]
        StreamProcessor[Stream Processor]
        BatchProcessor[Batch Processor]
    end
    
    subgraph "Storage"
        TransactionalDB[(Transactional DB)]
        AnalyticalDB[(Analytical DB)]
        Cache[(Cache)]
        SearchIndex[(Search Index)]
    end
    
    subgraph "Analytics & Reporting"
        Dashboard[Dashboards]
        Reports[Reports]
        ML[ML Pipeline]
    end
    
    UserActions --> Gateway
    APIRequests --> Gateway
    SystemEvents --> EventBus
    
    Gateway --> Services
    EventBus --> StreamProcessor
    Services --> LogCollector
    
    Services --> TransactionalDB
    StreamProcessor --> AnalyticalDB
    BatchProcessor --> AnalyticalDB
    Services --> Cache
    Services --> SearchIndex
    
    AnalyticalDB --> Dashboard
    AnalyticalDB --> Reports
    AnalyticalDB --> ML
    
    TransactionalDB --> BatchProcessor
```

## Deployment Architecture

```mermaid
graph TB
    subgraph "Production Environment"
        subgraph "Kubernetes Cluster"
            subgraph "Ingress"
                Ingress[Ingress Controller]
            end
            
            subgraph "Application Pods"
                Gateway_Pod[API Gateway Pod]
                Auth_Pod[Auth Service Pod]
                User_Pod[User Service Pod]
                Product_Pod[Product Service Pod]
                Order_Pod[Order Service Pod]
            end
            
            subgraph "Data Layer"
                DB_Pod[Database Pod]
                Cache_Pod[Cache Pod]
                Queue_Pod[Message Queue Pod]
            end
            
            subgraph "Monitoring"
                Prometheus[Prometheus]
                Grafana[Grafana]
                Jaeger[Jaeger]
            end
        end
        
        subgraph "External Storage"
            PersistentVolumes[(Persistent Volumes)]
            ObjectStorage[(Object Storage)]
        end
    end
    
    Internet --> Ingress
    Ingress --> Gateway_Pod
    Gateway_Pod --> Auth_Pod
    Gateway_Pod --> User_Pod
    Gateway_Pod --> Product_Pod
    Gateway_Pod --> Order_Pod
    
    User_Pod --> DB_Pod
    Product_Pod --> DB_Pod
    Order_Pod --> DB_Pod
    
    Auth_Pod --> Cache_Pod
    User_Pod --> Cache_Pod
    
    Order_Pod --> Queue_Pod
    
    DB_Pod --> PersistentVolumes
    Cache_Pod --> PersistentVolumes
    Queue_Pod --> PersistentVolumes
    
    Gateway_Pod --> ObjectStorage
    Product_Pod --> ObjectStorage
    
    Gateway_Pod --> Prometheus
    Auth_Pod --> Prometheus
    User_Pod --> Prometheus
    Product_Pod --> Prometheus
    Order_Pod --> Prometheus
    
    Prometheus --> Grafana
    Gateway_Pod --> Jaeger
    Auth_Pod --> Jaeger
    User_Pod --> Jaeger
    Product_Pod --> Jaeger
    Order_Pod --> Jaeger
```

## Security Architecture

```mermaid
graph TB
    subgraph "External Threats"
        Attackers[Potential Attackers]
        Bots[Malicious Bots]
    end
    
    subgraph "Security Layers"
        subgraph "Network Security"
            WAF[Web Application Firewall]
            DDoS[DDoS Protection]
            RateLimit[Rate Limiting]
        end
        
        subgraph "Application Security"
            AuthZ[Authorization]
            AuthN[Authentication]
            InputVal[Input Validation]
            OutputEnc[Output Encoding]
        end
        
        subgraph "Data Security"
            Encryption[Encryption at Rest]
            TLS[TLS in Transit]
            Secrets[Secret Management]
        end
        
        subgraph "Infrastructure Security"
            NetworkPol[Network Policies]
            RBAC[Role-Based Access Control]
            Scanning[Vulnerability Scanning]
        end
    end
    
    subgraph "Monitoring & Response"
        SIEM[Security Information and Event Management]
        Alerts[Security Alerts]
        Response[Incident Response]
    end
    
    Attackers --> WAF
    Bots --> DDoS
    
    WAF --> RateLimit
    DDoS --> RateLimit
    RateLimit --> AuthN
    AuthN --> AuthZ
    AuthZ --> InputVal
    InputVal --> OutputEnc
    
    OutputEnc --> Encryption
    OutputEnc --> TLS
    OutputEnc --> Secrets
    
    Encryption --> NetworkPol
    TLS --> RBAC
    Secrets --> Scanning
    
    NetworkPol --> SIEM
    RBAC --> SIEM
    Scanning --> SIEM
    
    SIEM --> Alerts
    Alerts --> Response
```
