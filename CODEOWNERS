# CODEOWNERS file for Avira System Design Documentation
# This file defines who must review changes to specific parts of the documentation

# Global fallback - Architecture team reviews everything by default
* @avira/architecture-team

# Architecture Decision Records - Require architecture team approval
/docs/adr/ @avira/architecture-team @avira/tech-leads

# System documentation - Require both architecture and relevant service teams
/docs/systems/ @avira/architecture-team @avira/platform-team

# Authentication service documentation
/docs/systems/auth-service/ @avira/auth-team @avira/security-team

# User service documentation  
/docs/systems/user-service/ @avira/user-team @avira/backend-team

# Product service documentation
/docs/systems/product-service/ @avira/product-team @avira/backend-team

# Order service documentation
/docs/systems/order-service/ @avira/order-team @avira/backend-team

# Security-related documentation - Security team must approve
/docs/guides/security-guidelines.md @avira/security-team @avira/architecture-team
/docs/runbooks/security-* @avira/security-team @avira/sre-team

# Infrastructure and deployment documentation
/docs/guides/deployment-procedures.md @avira/devops-team @avira/sre-team
/docs/runbooks/ @avira/sre-team @avira/devops-team

# Development guidelines - Tech leads must approve
/docs/guides/development-guidelines.md @avira/tech-leads @avira/architecture-team
/docs/guides/architecture-principles.md @avira/architecture-team @avira/tech-leads

# Diagrams - Architecture team and relevant domain experts
/docs/diagrams/ @avira/architecture-team

# Mermaid diagrams
/docs/diagrams/mermaid/ @avira/architecture-team @avira/platform-team

# PlantUML diagrams  
/docs/diagrams/plantuml/ @avira/architecture-team @avira/platform-team

# C4 model diagrams
/docs/diagrams/c4/ @avira/architecture-team @avira/tech-leads

# Configuration files - Platform team must approve
/mkdocs.yml @avira/platform-team @avira/devops-team
/.gitlab-ci.yml @avira/devops-team @avira/platform-team

# Root documentation files
/README.md @avira/architecture-team
/docs/index.md @avira/architecture-team

# Templates and governance
/docs/adr/template.md @avira/architecture-team
/.gitlab/ @avira/devops-team
/CODEOWNERS @avira/architecture-team @avira/devops-team
