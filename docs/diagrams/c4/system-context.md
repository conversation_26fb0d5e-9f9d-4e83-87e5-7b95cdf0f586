# C4 Model System Architecture

This page contains C4 model diagrams that provide different levels of architectural detail for our system.

## System Context Diagram

```plantuml
@startuml system-context
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Context.puml

title System Context Diagram for Avira Platform

Person(customer, "Customer", "End user who uses our platform to purchase products and services")
Person(admin, "Administrator", "Internal user who manages the platform, products, and customer support")
Person(partner, "Business Partner", "External partner who integrates with our APIs")

System(avira_platform, "Avira Platform", "Provides product catalog, order management, user accounts, and payment processing")

System_Ext(payment_gateway, "Payment Gateway", "External payment processing service (Stripe, PayPal)")
System_Ext(email_service, "Email Service", "External email delivery service (SendGrid, AWS SES)")
System_Ext(sms_service, "SMS Service", "External SMS delivery service (Twilio)")
System_Ext(analytics, "Analytics Platform", "External analytics and monitoring (Google Analytics, Mixpanel)")

Rel(customer, avira_platform, "Uses", "HTTPS")
Rel(admin, avira_platform, "Manages", "HTTPS")
Rel(partner, avira_platform, "Integrates with", "HTTPS/REST API")

Rel(avira_platform, payment_gateway, "Processes payments", "HTTPS/REST API")
Rel(avira_platform, email_service, "Sends emails", "HTTPS/REST API")
Rel(avira_platform, sms_service, "Sends SMS", "HTTPS/REST API")
Rel(avira_platform, analytics, "Sends events", "HTTPS/REST API")

@enduml
```

## Container Diagram

```plantuml
@startuml container-diagram
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

title Container Diagram for Avira Platform

Person(customer, "Customer")
Person(admin, "Administrator")

System_Boundary(avira_platform, "Avira Platform") {
    Container(web_app, "Web Application", "React, TypeScript", "Provides customer-facing web interface")
    Container(mobile_app, "Mobile Application", "React Native", "Provides customer-facing mobile interface")
    Container(admin_app, "Admin Portal", "React, TypeScript", "Provides administrative interface")
    
    Container(api_gateway, "API Gateway", "Kong", "Routes requests, handles authentication, rate limiting")
    
    Container(auth_service, "Authentication Service", "Node.js, Express", "Handles user authentication and authorization")
    Container(user_service, "User Service", "Python, FastAPI", "Manages user profiles and preferences")
    Container(product_service, "Product Service", "Java, Spring Boot", "Manages product catalog and inventory")
    Container(order_service, "Order Service", "Go, Gin", "Handles order processing and fulfillment")
    Container(payment_service, "Payment Service", "Python, FastAPI", "Processes payments and billing")
    Container(notification_service, "Notification Service", "Node.js, Express", "Sends emails, SMS, and push notifications")
    
    ContainerDb(user_db, "User Database", "PostgreSQL", "Stores user accounts and profiles")
    ContainerDb(product_db, "Product Database", "PostgreSQL", "Stores product catalog and inventory")
    ContainerDb(order_db, "Order Database", "PostgreSQL", "Stores orders and transactions")
    ContainerDb(cache, "Cache", "Redis", "Caches frequently accessed data")
    Container(message_queue, "Message Queue", "Apache Kafka", "Handles asynchronous communication")
}

System_Ext(payment_gateway, "Payment Gateway")
System_Ext(email_service, "Email Service")
System_Ext(sms_service, "SMS Service")

Rel(customer, web_app, "Uses", "HTTPS")
Rel(customer, mobile_app, "Uses", "HTTPS")
Rel(admin, admin_app, "Uses", "HTTPS")

Rel(web_app, api_gateway, "Makes API calls", "HTTPS/REST")
Rel(mobile_app, api_gateway, "Makes API calls", "HTTPS/REST")
Rel(admin_app, api_gateway, "Makes API calls", "HTTPS/REST")

Rel(api_gateway, auth_service, "Routes requests", "HTTP/REST")
Rel(api_gateway, user_service, "Routes requests", "HTTP/REST")
Rel(api_gateway, product_service, "Routes requests", "HTTP/REST")
Rel(api_gateway, order_service, "Routes requests", "HTTP/REST")
Rel(api_gateway, payment_service, "Routes requests", "HTTP/REST")

Rel(auth_service, user_db, "Reads/Writes", "SQL")
Rel(auth_service, cache, "Reads/Writes", "Redis Protocol")
Rel(user_service, user_db, "Reads/Writes", "SQL")
Rel(user_service, cache, "Reads/Writes", "Redis Protocol")
Rel(product_service, product_db, "Reads/Writes", "SQL")
Rel(product_service, cache, "Reads/Writes", "Redis Protocol")
Rel(order_service, order_db, "Reads/Writes", "SQL")
Rel(payment_service, order_db, "Reads/Writes", "SQL")

Rel(order_service, message_queue, "Publishes events", "Kafka Protocol")
Rel(notification_service, message_queue, "Consumes events", "Kafka Protocol")
Rel(user_service, message_queue, "Publishes events", "Kafka Protocol")
Rel(product_service, message_queue, "Publishes events", "Kafka Protocol")

Rel(payment_service, payment_gateway, "Processes payments", "HTTPS/REST")
Rel(notification_service, email_service, "Sends emails", "HTTPS/REST")
Rel(notification_service, sms_service, "Sends SMS", "HTTPS/REST")

@enduml
```

## Component Diagram - Order Service

```plantuml
@startuml order-service-components
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

title Component Diagram for Order Service

Container(api_gateway, "API Gateway", "Kong")
Container(payment_service, "Payment Service", "Python, FastAPI")
Container(product_service, "Product Service", "Java, Spring Boot")
Container(notification_service, "Notification Service", "Node.js")
ContainerDb(order_db, "Order Database", "PostgreSQL")
Container(message_queue, "Message Queue", "Apache Kafka")

Container_Boundary(order_service, "Order Service") {
    Component(order_controller, "Order Controller", "Go, Gin", "Handles HTTP requests for order operations")
    Component(order_service_comp, "Order Service", "Go", "Implements order business logic")
    Component(payment_client, "Payment Client", "Go", "Communicates with Payment Service")
    Component(product_client, "Product Client", "Go", "Communicates with Product Service")
    Component(order_repository, "Order Repository", "Go, GORM", "Handles order data persistence")
    Component(event_publisher, "Event Publisher", "Go, Kafka Client", "Publishes order events")
    Component(order_validator, "Order Validator", "Go", "Validates order data and business rules")
    Component(inventory_manager, "Inventory Manager", "Go", "Manages inventory reservations")
}

Rel(api_gateway, order_controller, "Routes requests", "HTTP/REST")
Rel(order_controller, order_service_comp, "Uses")
Rel(order_service_comp, order_validator, "Validates orders")
Rel(order_service_comp, inventory_manager, "Manages inventory")
Rel(order_service_comp, payment_client, "Processes payments")
Rel(order_service_comp, product_client, "Gets product info")
Rel(order_service_comp, order_repository, "Persists data")
Rel(order_service_comp, event_publisher, "Publishes events")

Rel(payment_client, payment_service, "Makes API calls", "HTTP/REST")
Rel(product_client, product_service, "Makes API calls", "HTTP/REST")
Rel(order_repository, order_db, "Reads/Writes", "SQL")
Rel(event_publisher, message_queue, "Publishes", "Kafka Protocol")

@enduml
```

## Deployment Diagram

```plantuml
@startuml deployment-diagram
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Deployment.puml

title Deployment Diagram for Avira Platform

Deployment_Node(aws_cloud, "AWS Cloud", "Amazon Web Services") {
    Deployment_Node(vpc, "VPC", "Virtual Private Cloud") {
        Deployment_Node(public_subnet, "Public Subnet", "Internet-facing resources") {
            Deployment_Node(alb, "Application Load Balancer", "AWS ALB") {
                Container(load_balancer, "Load Balancer", "AWS ALB", "Distributes traffic")
            }
            
            Deployment_Node(nat_gateway, "NAT Gateway", "AWS NAT Gateway") {
                Container(nat, "NAT Gateway", "AWS NAT", "Outbound internet access")
            }
        }
        
        Deployment_Node(private_subnet, "Private Subnet", "Internal resources") {
            Deployment_Node(eks_cluster, "EKS Cluster", "Kubernetes") {
                Deployment_Node(worker_nodes, "Worker Nodes", "EC2 instances") {
                    Container(api_gateway, "API Gateway", "Kong", "Routes and manages API requests")
                    Container(auth_service, "Auth Service", "Node.js", "Authentication and authorization")
                    Container(user_service, "User Service", "Python", "User management")
                    Container(product_service, "Product Service", "Java", "Product catalog")
                    Container(order_service, "Order Service", "Go", "Order processing")
                    Container(payment_service, "Payment Service", "Python", "Payment processing")
                }
            }
            
            Deployment_Node(rds_cluster, "RDS Cluster", "PostgreSQL") {
                ContainerDb(primary_db, "Primary Database", "PostgreSQL", "Main application database")
                ContainerDb(read_replica, "Read Replica", "PostgreSQL", "Read-only database replica")
            }
            
            Deployment_Node(elasticache, "ElastiCache", "Redis") {
                Container(redis_cluster, "Redis Cluster", "Redis", "Caching and session storage")
            }
            
            Deployment_Node(msk, "MSK", "Apache Kafka") {
                Container(kafka_cluster, "Kafka Cluster", "Apache Kafka", "Message streaming")
            }
        }
    }
}

Deployment_Node(external_services, "External Services") {
    Container(stripe, "Stripe", "Payment Gateway", "Payment processing")
    Container(sendgrid, "SendGrid", "Email Service", "Email delivery")
    Container(twilio, "Twilio", "SMS Service", "SMS delivery")
}

Deployment_Node(monitoring, "Monitoring & Observability") {
    Container(prometheus, "Prometheus", "Metrics", "Metrics collection")
    Container(grafana, "Grafana", "Dashboards", "Metrics visualization")
    Container(jaeger, "Jaeger", "Tracing", "Distributed tracing")
    Container(elk, "ELK Stack", "Logging", "Centralized logging")
}

Rel(load_balancer, api_gateway, "Routes traffic", "HTTPS")
Rel(api_gateway, auth_service, "Authenticates", "HTTP")
Rel(api_gateway, user_service, "User operations", "HTTP")
Rel(api_gateway, product_service, "Product operations", "HTTP")
Rel(api_gateway, order_service, "Order operations", "HTTP")
Rel(api_gateway, payment_service, "Payment operations", "HTTP")

Rel(auth_service, primary_db, "User auth data", "SQL")
Rel(user_service, primary_db, "User data", "SQL")
Rel(product_service, primary_db, "Product data", "SQL")
Rel(order_service, primary_db, "Order data", "SQL")

Rel(auth_service, redis_cluster, "Sessions", "Redis")
Rel(user_service, redis_cluster, "Cache", "Redis")
Rel(product_service, redis_cluster, "Cache", "Redis")

Rel(order_service, kafka_cluster, "Events", "Kafka")
Rel(user_service, kafka_cluster, "Events", "Kafka")
Rel(product_service, kafka_cluster, "Events", "Kafka")

Rel(payment_service, stripe, "Payment processing", "HTTPS")
Rel(auth_service, sendgrid, "Email notifications", "HTTPS")
Rel(auth_service, twilio, "SMS notifications", "HTTPS")

@enduml
```
